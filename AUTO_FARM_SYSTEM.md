# Auto Farm System Documentation

## Overview
The Auto Farm System is a new feature added to the Grow a Garden tool that automatically harvests plants across all farm areas. It has been implemented as a third tab in the existing tabbed interface.

## Features

### 1. Automatic Plant Harvesting
- **Scans all 6 farm subfolders** in `workspace.Farm`
- **Searches for `Plants_Physical` objects** containing harvestable plant models
- **Attempts to harvest each plant** using the provided `HarvestPlant` function
- **Continues scanning every 5 seconds** when enabled

### 2. Error Handling
- **Skips plants without ProximityPrompt** - no retries to prevent infinite loops
- **Graceful error handling** with pcall wrappers around critical operations
- **Validates workspace structure** before attempting to scan
- **Type checking** to ensure objects are proper Models/Folders

### 3. User Interface
- **Toggle checkbox** to enable/disable auto farming
- **Real-time status display** showing current state (Running/Stopped)
- **Harvest counter** tracking total plants harvested
- **Last scan timestamp** showing time since last scan
- **Informational text** explaining the system's operation

### 4. Performance Optimizations
- **5-second scan interval** to prevent excessive processing
- **Small delays between harvests** (0.05s) to prevent overwhelming the server
- **Proper cleanup** when the system is disabled
- **Memory leak prevention** with connection management

## Implementation Details

### Core Functions

#### `HarvestPlant(Plant: Model)`
```lua
local function HarvestPlant(Plant)
    if not Plant or not Plant:IsA("Model") then return false end
    
    local Prompt = Plant:FindFirstChild("ProximityPrompt", true)
    
    -- Check if it can be harvested
    if not Prompt then return false end
    
    pcall(function()
        fireproximityprompt(Prompt)
        AutoFarmSystem.harvestCount = AutoFarmSystem.harvestCount + 1
    end)
    
    return true
end
```

#### `ScanAndHarvestFarms()`
- Iterates through all children of `workspace.Farm`
- Looks for `Plants_Physical` folders in each farm subfolder
- Attempts to harvest all Model objects found within
- Returns the number of plants harvested in the current scan

### System Variables
```lua
local AutoFarmSystem = {
    isActive = false,        -- Whether auto farming is enabled
    connections = {},        -- Event connections for cleanup
    harvestCount = 0,        -- Total plants harvested
    lastScanTime = 0        -- Timestamp of last scan
}
```

### UI Components
- **Auto Farm Tab**: Third tab in the interface (positioned at x=260)
- **Enable Checkbox**: Toggle to start/stop auto farming
- **Status Labels**: Real-time feedback on system state
- **Info Label**: Explains system operation to users

## Usage Instructions

1. **Open the application** - The Auto Farm tab will be visible as the third tab
2. **Click the Auto Farm tab** to switch to the auto farming interface
3. **Check "Auto Farm Enabled"** to start the automatic harvesting
4. **Monitor the status** - Watch the harvest counter and status updates
5. **Uncheck to stop** - Disable the checkbox to stop auto farming

## Technical Specifications

### Scan Frequency
- **Main loop**: Runs every 5 seconds when enabled
- **Status updates**: Every 0.1 seconds for responsive UI
- **Harvest delay**: 0.05 seconds between individual plant harvests

### Workspace Structure Expected
```
workspace
└── Farm
    ├── Farm1 (or any subfolder name)
    │   └── Plants_Physical
    │       ├── PlantModel1
    │       ├── PlantModel2
    │       └── ...
    ├── Farm2
    │   └── Plants_Physical
    │       └── ...
    └── ... (up to 6 farm areas)
```

### Error Conditions Handled
- **Missing Farm folder**: System warns and continues
- **Missing Plants_Physical**: Skips that farm area
- **Invalid plant models**: Skips non-Model objects
- **Missing ProximityPrompt**: Skips unharvestableplants
- **System disabled mid-scan**: Breaks out of loops immediately

## Integration with Existing System

### Tab System
- **Extends existing tab architecture** without breaking current functionality
- **Uses same UI creation functions** (`CreateTab`, `CreateCheckbox`, `CreateLabel`)
- **Follows same styling** and color scheme as other tabs

### Cleanup Integration
- **Added to main cleanup function** to prevent memory leaks
- **Properly disconnects** all event connections when GUI is destroyed
- **Stops all auto farm operations** when system shuts down

### Window Sizing
- **Increased window width** from 520px to 600px to accommodate third tab
- **Maintains responsive design** and proper tab spacing

## Performance Impact

### Minimal Resource Usage
- **Event-driven updates** rather than continuous polling
- **Efficient scanning** with early breaks when disabled
- **Proper error handling** prevents crashes or infinite loops
- **Memory management** prevents accumulation of unused connections

### Server Considerations
- **Respects server limits** with delays between harvests
- **Uses existing game functions** (`fireproximityprompt`)
- **No excessive remote calls** - only harvests when plants are ready

## Future Enhancements

### Potential Improvements
1. **Selective farm area targeting** - Choose which farms to scan
2. **Plant type filtering** - Only harvest specific plant types
3. **Harvest scheduling** - Set specific times for auto farming
4. **Statistics tracking** - Detailed harvest logs and analytics
5. **Sound notifications** - Audio feedback for successful harvests

### Compatibility
- **Backward compatible** with all existing features
- **Non-intrusive** - doesn't affect shop monitoring or auto-buy systems
- **Modular design** - can be easily disabled or removed if needed

## Testing

A test script (`test_autofarm.lua`) is provided to verify the core harvesting logic works correctly with mock data structures.

## Support

The auto farm system integrates seamlessly with the existing Grow a Garden tool and follows the same architectural patterns for reliability and maintainability.
